import { useState, useCallback, useMemo } from 'react';
import { Modal, message } from '@blmcp/ui';
import FilterConditionList from './components/FilterConditionList';
import FilterConfigPanel from './components/FilterConfigPanel';
import './index.less';

// 生成唯一ID的函数
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

export interface FilterCondition {
  id: string;
  title: string;
  componentName:
    | 'DatePickerFilter'
    | 'RangeOfIntervalsFilter'
    | 'InputFilter'
    | 'ListFilter'
    | null;
  selectedCharts: string[];
  selectedFields: Record<string, string>;
  // 新增：选中图表的完整信息
  selectedChartsItem: any[];
  // 新增：选中字段的完整信息
  selectedFieldsInfo: Record<
    string,
    {
      title: string;
      key: string;
      dataType: number;
      isAggr: number;
      columnId: any;
      biType: any;
    }
  >;
  isValid?: boolean;
  errorMessage?: string[];
}

interface PartialFilterModalProps {
  isModalOpen: boolean;
  handleCloseModal: () => void;
  onSave?: (conditions: FilterCondition[]) => void;
  initialData?: FilterCondition[];
  chartComList?: any[];
}

const PartialFilterModal = ({
  isModalOpen,
  handleCloseModal,
  onSave,
  initialData = [],
  chartComList,
}: PartialFilterModalProps) => {
  // 查询条件list
  const [conditions, setConditions] = useState<FilterCondition[]>(initialData);
  // 当前选中的查询条件id
  const [activeConditionId, setActiveConditionId] = useState<string>('');

  // 添加新的查询条件
  const handleAddCondition = useCallback(() => {
    if (conditions.length >= 5) return;
    // 新增查询条件默认id自动累加
    const newCondition: FilterCondition = {
      id: generateId(),
      title: '查询条件',
      componentName: null,
      selectedCharts: [],
      selectedFields: {},
      selectedChartsItem: [],
      selectedFieldsInfo: {},
    };

    const newConditions = [...conditions, newCondition];
    setConditions(newConditions);
    setActiveConditionId(newCondition.id);
  }, [conditions]);

  // 删除查询条件
  const handleDeleteCondition = useCallback(
    (id: string) => {
      const newConditions = conditions.filter(
        (condition) => condition.id !== id,
      );
      // 记录删除过的查询条件数量
      setConditions(newConditions);

      // 如果删除的是当前选中的条件，切换到第一个条件
      if (activeConditionId === id) {
        setActiveConditionId(
          newConditions.length > 0 ? newConditions[0].id : '',
        );
      }
    },
    [conditions, activeConditionId],
  );

  // 更新查询条件名称
  const handleUpdateConditionName = useCallback((id: string, title: string) => {
    setConditions((prev) =>
      prev.map((condition) =>
        condition.id === id ? { ...condition, title } : condition,
      ),
    );
  }, []);

  // 更新查询条件配置
  const handleUpdateCondition = useCallback(
    (id: string, updates: Partial<FilterCondition>) => {
      setConditions((prev) =>
        prev.map((condition) =>
          condition.id === id ? { ...condition, ...updates } : condition,
        ),
      );
    },
    [],
  );

  // 验证配置完整性
  const validateConditions = useCallback(() => {
    const errors: { id: string; message: string }[] = [];
    const nameMap = new Map<string, string[]>();
    if (conditions?.length === 0)
      errors.push({ id: '', message: '至少添加一个查询条件' });

    conditions.forEach((condition) => {
      // 检查名称重复
      if (!nameMap.has(condition.title)) {
        nameMap.set(condition.title, []);
      }
      nameMap.get(condition.title)!.push(condition.id);

      // 检查配置完整性
      if (condition.selectedCharts.length === 0) {
        errors.push({ id: condition.id, message: '请先选择关联图表及字段' });
      } else {
        const hasUnselectedFields = condition.selectedCharts.some(
          (chartId) => !condition.selectedFields[chartId],
        );
        if (hasUnselectedFields) {
          errors.push({ id: condition.id, message: '请先选择关联图表及字段' });
        }
      }
    });

    // 更新条件状态
    setConditions((prev) =>
      prev.map((condition) => {
        // const error = errors.find((e) => e.id === condition.id);
        const matchedErrors = errors.filter((e) => e.id === condition.id);
        console.log(matchedErrors, 'error----', condition, prev, errors);
        return {
          ...condition,
          isValid: matchedErrors.length === 0,
          errorMessage: matchedErrors.map((e) => e.message),
        };
      }),
    );
    console.log(nameMap, 'nameMap----', conditions, errors);
    if (errors?.length > 0) {
      const uniqueMessages = [...new Set(errors?.map((item) => item.message))];
      const errorMessage = uniqueMessages.join('<br/>');
      message.error({
        content: <div dangerouslySetInnerHTML={{ __html: errorMessage }} />,
      });
    }
    return errors.length === 0;
  }, [conditions]);

  // 保存处理
  const handleSave = useCallback(() => {
    if (validateConditions()) {
      onSave?.(conditions);
      handleCloseModal();
      message.success('保存成功');
    }
  }, [conditions, validateConditions, onSave, handleCloseModal]);

  // 当前激活的条件
  const activeCondition = useMemo(
    () => conditions.find((condition) => condition.id === activeConditionId),
    [conditions, activeConditionId],
  );

  return (
    <Modal
      title="条件设置"
      open={isModalOpen}
      onCancel={handleCloseModal}
      onOk={handleSave}
      width={1160}
      className="partial-filter-modal"
      centered
    >
      <div className="partial-filter-content">
        <div className="filter-conditions-panel">
          <FilterConditionList
            conditions={conditions}
            activeConditionId={activeConditionId}
            onAddCondition={handleAddCondition}
            onDeleteCondition={handleDeleteCondition}
            onSelectCondition={setActiveConditionId}
            onUpdateConditionName={handleUpdateConditionName}
          />
        </div>
        <div className="filter-config-panel">
          <FilterConfigPanel
            condition={activeCondition}
            onUpdateCondition={handleUpdateCondition}
            chartComList={chartComList}
          />
        </div>
      </div>
    </Modal>
  );
};

export default PartialFilterModal;
