import { EditOutlined } from '@ant-design/icons';
import React, { useState, useEffect, useCallback } from 'react';
import { getProjectSchema, walkerComponents } from '@/pages/lego/utils';
import { store as componentStore } from '@/pages/lego/hooks/useComponent';
import { queryDataSourceList } from '@/pages/lego/api/filterEditor';
import PartialFilterModal from './PartialFilterModal';

interface FilterItemProps {
  id?: string;
  componentName: string;
  props: any;
}

const getSetting = function (node: any): FilterItemProps[] {
  const schema = node.schema;
  const setting: any = [];
  const rowSchema = schema.children?.[0] || {};
  rowSchema.children?.forEach((v) => {
    const child = v.children?.[0];
    if (child) {
      setting.push({
        id: child.id,
        componentName: child.componentName,
        props: child.props,
      });
    }
  });
  return setting;
};

const setSetting = function (node: any, items: FilterItemProps[]) {
  const uuid = node.getPropValue('uuid');
  const reportId = node.getPropValue('reportId');
  const schema = node.schema;
  const links = items.reduce((acc: any, cur) => {
    acc.push(...cur.props.filterLinkComponents.map((v) => v.componentId));
    return acc;
  }, []);
  const filterLinkComponents = [...new Set(links)];
  schema.children = [
    {
      componentName: 'FDRow',
      props: {
        uuid,
        reportId,
        isPartialContainerFilter: true,
      },
      children: items
        .map((item) => {
          return {
            componentName: 'FDCell',
            props: {
              uuid,
              reportId,
            },
            children: [
              {
                id: item.id,
                componentName: item.componentName,
                props: {
                  uuid,
                  reportId,
                  partialContainerFilterId: node.id,
                  ...item.props,
                },
              },
            ],
          };
        })
        .concat([
          {
            componentName: 'FDCell',
            props: {
              uuid,
              reportId,
              // @ts-expect-error
              isDefaultFilter: true,
            },
            children: [
              {
                id: undefined,
                componentName: 'SearchButton',
                props: {
                  uuid,
                  reportId,
                  partialContainerFilterId: node.id,
                  isDefaultFilter: true,
                  filterLinkComponents,
                },
              },
            ],
          },
        ]),
    },
  ];

  node.replaceWith(schema);
};

export default function FilterEditor(props) {
  // 条件设置弹窗状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 图表数据list
  const [chartComList, setChartComList] = useState([]);
  // 数据集全量数据
  const [allDatasetsList, setAllDatasetsList] = useState([]);
  const [newFilters, setNewFilters] = useState([]);

  const { config, project } = window.AliLowCodeEngine;
  // 报告id
  const reportId = config.get('reportId');
  // 当前组件节点
  const node = props.node?._children?.children?.[0];

  if (!node || node.componentName !== 'PartialContainerFilter') return null;

  // 组件配置项
  const componentProps = node.schema.props || {};
  // 筛选器列表, 已有的条件需要回填
  const filterList = getSetting(node);
  console.log('filterList', filterList);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  // 当前页面图表list
  const handleGetChartComList = () => {
    let chartArr: any[] = [];
    queryDataSourceList().then((res) => {
      if (res?.code === 1) {
        // setAllDatasetsList(res.data);
        walkerComponents(getProjectSchema(), (item, meta, metaMap) => {
          console.log(
            item,
            '11111',
            meta.title,
            meta,
            '---',
            metaMap,
            '=======walkerComponents',
            getProjectSchema(),
            item?.props?.dataSetConfig?.dataSourceId,
          );
          if (meta?.compliance) {
            const dataSourceName = res.data.find(
              (v: any) => v.id === item?.props?.dataSetConfig?.dataSourceId,
            )?.name;
            chartArr.push({
              ...meta,
              dataSourceId: item?.props?.dataSetConfig?.dataSourceId,
              dataSourceName,
              id: item.id,
            });
          }
        });
        setChartComList(chartArr);
      }
    });
  };
  const handleOpenModal = () => {
    setIsModalOpen(true);
    handleGetChartComList();
  };
  // 局部筛选器保存函数
  const onSave = (conditions: any) => {
    const newFilters = conditions.map((item: any) => ({
      componentName: item?.componentName,
      props: {
        title: item.title,
        filterLinkComponents: item.selectedChartsItem?.map(
          (selectItem: any) => ({
            componentId: selectItem?.id,
            bingdingChartIds: selectItem?.elementId,
            datasetId: selectItem?.dataSourceId,
            ...item.selectedFieldsInfo[selectItem?.id],
          }),
        ),
      },
    }));
    console.log(conditions, '生成的 newFilters:', newFilters, [
      ...filterList,
      ...newFilters,
    ]);

    setSetting(node, [...filterList, ...newFilters]);
  };
  console.log('newFilters', newFilters);
  return (
    <div className="lc-borders-action">
      <EditOutlined
        onClick={() => {
          handleOpenModal();
        }}
      />
      <PartialFilterModal
        isModalOpen={isModalOpen}
        handleCloseModal={handleCloseModal}
        onSave={onSave}
        chartComList={chartComList}
      ></PartialFilterModal>
    </div>
  );
}
