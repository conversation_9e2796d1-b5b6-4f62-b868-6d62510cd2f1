/**
 * 日期组件
 */
import { useEffect, useState, useRef, useCallback } from 'react';
import dayjs from 'dayjs';
import { useLegoReport } from '@blm/bi-lego-sdk/dist/es/utils';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import useComponent from '@/pages/lego/hooks/useComponent';
import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import { getOtherPresets } from '../../components/Filter/DatePickerFilter/tools';
import FilterWrapper from '../wrapper/FilterWrapper';
import { DEFAULT_DATE_RANGE } from './constants/dateRangeOptions';
import { BLMIconFont } from '@blmcp/ui';
interface DatePickerFilterProps {
  title?: string;
  dataSetConfig: any;
  __id: any;
  componentId: any;
  defaultValue: any;
  dateRange?: number;
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateOnly: true,
      indexDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {
      // 拖拽进入的，还需要配置数据集
      type: 'drop-component',
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
  // 别名
  title: {
    componentName: 'AliasSetter',
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DatePickerFilter = (props: DatePickerFilterProps) => {
  const {
    __id,
    componentId,
    defaultValue,
    dateRange = DEFAULT_DATE_RANGE,
    uuid,
  } = props;
  console.log('props', props);
  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());
  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const dateRangeRef = useRef(dateRange);
  const filterId = __id ?? componentId ?? '';
  // const { dimensionInfo } = dataSetConfig ?? {};
  // const dimension = dimensionInfo?.[0];
  const presets = getOtherPresets();
  // const presetList = getOtherPresets();
  const [_, setMeta] = useComponent(filterId);
  const [reportMeta] = useLegoReport(uuid);
  const isEdit = props.__designMode === 'design';
  // 时间单位
  const dateUnitRef = useRef(6);

  useEffect(() => {
    setMeta(
      {
        query() {
          relationCenter.notify('all');
        },
      },
      true,
    );
  }, []);

  const getDefaultDateItem = useCallback(() => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 清空场景
    if (dateType === 'all') {
      return presets.find((item) => item.type === 'clear-time');
    }

    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  }, [defaultValue, presets]);

  const defaultDateItem = getDefaultDateItem();
  // 配置项变更手动重置为当前默认值，并刷新图表数据
  const handleDateChange = useCallback(
    (val, newField) => {
      if (dateRangeRef.current !== dateRange) {
        dateRangeRef.current = dateRange;
        setKey(Date.now());
      }
      if (defaultValue?.dateType === 'customDate') {
        defaultValueRef.current = defaultValue;
        temp_dateMap[filterId] = {
          value:
            defaultValue?.value ||
            presets.find((item) => item.type === 'last-7'),
          type: 6, // CUSTOM 类型
        };
        dateUnitRef.current = 6;
      } else if (
        JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
      ) {
        defaultValueRef.current = defaultValue;
        dateUnitRef.current = defaultDateItem.timeType;
        temp_dateMap[filterId] = null;
        setKey(Date.now());
      }
    },
    [dateRange, defaultValue, filterId, presets],
  );
  const handleFieldLabel = (value: any[]) => {
    return value?.length
      ? [
          dayjs(value[0]).startOf('day').format('YYYY-MM-DD') +
            ' ~ ' +
            dayjs(value[1]).endOf('day').format('YYYY-MM-DD'),
        ]
      : [];
  };
  const handleDefaultValue = (value: any[]) => {
    return value?.length
      ? [
          dayjs(value[0]).startOf('day').valueOf(),
          dayjs(value[1]).endOf('day').valueOf(),
        ]
      : [];
  };
  return (
    <FilterWrapper
      componentProps={props}
      label={isEdit || reportMeta.oldVersion > '2.6.0'}
      defaultValue={handleDefaultValue(defaultDateItem?.value)}
      onChange={handleDateChange}
      handleFieldLabel={handleFieldLabel}
      filterExtendField={(field, resetType) => {
        return {
          dateFilterRelativeUnit: resetType
            ? defaultDateItem.timeType
            : dateUnitRef.current,
        };
      }}
    >
      {({ value, onChange, fieldItem }) => {
        let valueChange = [] as any;
        if (value?.length) {
          valueChange = [dayjs(value[0]).toDate(), dayjs(value[1]).toDate()];
        }
        return (
          <DatePickerFilterView
            key={key}
            pickerType="picker-filter"
            handleChange={(date, dateUnit) => {
              dateUnitRef.current = dateUnit;
              onChange(date, dateUnit);
            }}
            defaultValue={defaultDateItem}
            maxStep={dateRange - 1}
            value={valueChange}
            placeholder={
              reportMeta.oldVersion > '2.6.0' &&
              (props.title || fieldItem.title || '日期')
            }
            renderExtraFooter={() => {
              return (
                <div style={{ color: '#86909C' }}>
                  <BLMIconFont type="BLM-ic-caution-o" />
                  <span style={{ marginLeft: '8px' }}>
                    搜索数据源是实时数据，可以查询到截止当前所有数据
                  </span>
                </div>
              );
            }}
          />
        );
      }}
    </FilterWrapper>
  );
};

DatePickerFilter.displayName = '时间筛选器';
