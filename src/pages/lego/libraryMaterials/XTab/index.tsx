import React, { useMemo, useState, useEffect, useRef } from 'react';
import './style.less';
import { Dropdown, Tooltip, Popover } from '@blmcp/ui';
import { DashOutlined } from '@ant-design/icons';
import ResizeObserver from 'resize-observer-polyfill';
import { store } from '@/pages/lego/hooks/useComponent';
import { ComponentProps } from '@/pages/lego/type';
import isMobile from '../../utils/isMobile';
import { ExclamationCircleOutlined } from '@ant-design/icons';
// import { getTextyWidthAndHeight } from '../../utils/css';
import layout from './layout';

export const LinkSetterComponent = {
  items: {
    componentName: 'TabSetter',
    defaultValue: [
      {
        key: 'item1',
        label: '未命名',
        richTips: '',
      },
      {
        key: 'item2',
        label: '未命名',
        richTips: '',
      },
    ],
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本 6选项卡
  componentType: 6,
};

export const ComponentRule = {
  isContainer: true,
  nestingRule: {
    childWhitelist: ['XTabItem'],
  },
};

export const MergedComponentMate = {
  configure: {
    advanced: {
      initialChildren: [
        {
          componentName: 'XTabItem',
          props: { key: 'item1', label: '未命名', placeholder: ' ' },
          selected: 'parent',
        },
        {
          componentName: 'XTabItem',
          props: { key: 'item2', label: '未命名', placeholder: ' ' },
          selected: 'parent',
        },
      ],
    },
  },
};

interface XTabItemProps {
  key: string;
  label: string;
  rendering?: boolean;
}
interface XTabProps extends ComponentProps<any> {
  items: XTabItemProps[];
  // children: XTabItemProps[];
}

function hasVerticalScroll(element: Element) {
  if (!element) return false;
  return element.scrollWidth > element.clientWidth;
}

// 获取不在可视区的item
function getNotVisibleItem(element: Element, tabItems: XTabItemProps[]) {
  // 获取不在可视区的item
  const childItem = element?.children || [];
  // 获取item 容器宽度
  const containerWidth = element?.clientWidth - 50;
  // 获取横行滚动距离
  const scrollLeft = element?.scrollLeft || 0;

  let sumWidth = 0;
  let item = [];
  for (let i = 0; i < childItem.length; i++) {
    const width = childItem[i].clientWidth;
    // 当前元素在滚动条外面
    if (sumWidth + width - 50 < scrollLeft) {
      item.push({
        ...tabItems[i],
        width: sumWidth,
      });
    }

    if (sumWidth > containerWidth + scrollLeft) {
      item.push({
        ...tabItems[i],
        width: sumWidth,
      });
    }
    sumWidth += width;
  }
  return item;
}

export const XTab = function (props: XTabProps) {
  const { items, children, componentId, __id } = props;
  const chartId = componentId ?? __id;
  const isEdit = props.__designMode === 'design';
  const timeRef = useRef(undefined);
  const xtabRef = useRef<HTMLDivElement>(null);
  const [curKey, setKey] = useState('');
  const tabItemMap = useRef<{ [key: string]: XTabItemProps }>({});

  // 下拉菜单item
  const [dropdownMenu, setDropdownMenu] = useState<any>([]);
  // 标签列表ref
  const tabListRef = React.useRef<Element>();
  // 是否显示更多
  const [showMore, setShowMore] = useState(false);

  // 检测标签列表宽度改变
  useEffect(() => {
    let resizeObserver = new ResizeObserver(function (entries) {
      setShowMore(false);
      setTimeout(() => {
        if (hasVerticalScroll(tabListRef.current)) {
          // 再执行下item 可见数，如果没有，还是不显示
          const tabList = getNotVisibleItem(
            tabListRef.current as Element,
            items,
          );
          setShowMore(!!tabList.length);
        } else {
          setShowMore(false);
        }
      }, 100);

      // 编辑态需要检测宽度变化，通知内部组件进行换行处理
      if (isEdit) {
        // 元素隐藏也不需要检测
        if (entries.some((s) => s.contentRect.height && s.contentRect.width)) {
          clearTimeout(timeRef.current);
          timeRef.current = window.setTimeout(() => {
            const node = window.__lego_getNodeById(chartId);
            const curKey = store.get(chartId)?.componentTempProps?.tabActiveKey;
            layout(node, curKey);
          }, 100);
        }
      }
    });
    resizeObserver.observe(tabListRef.current as Element);
    return function () {
      resizeObserver?.disconnect?.();
    };
  }, [items]);

  /**
   * 切换标签页项
   *
   * @param key 标签页的键
   * @returns 无返回值
   */
  const switchTabItem = (key: string) => {
    xtabRef.current?.classList.add('xtab-switching');

    // 检测key是否有效
    if (!items?.find((item) => item.key === key)) {
      // eslint-disable-next-line no-param-reassign
      key = items?.[0]?.key || '';
    }

    setKey(key);
    if (tabItemMap.current[key]) {
      tabItemMap.current[key].rendering = true;
    }

    // 临时传给组件信息，好让其他地方获取
    store.merge(chartId, { componentTempProps: { tabActiveKey: key } });

    setTimeout(() => {
      xtabRef.current?.classList.remove('xtab-switching');
    }, 20);
  };

  useEffect(() => {
    items?.forEach((item) => {
      if (!tabItemMap.current[item.key]) {
        tabItemMap.current[item.key] = item;
      }
    });

    switchTabItem(curKey || items?.[0]?.key);
    // eslint-disable-next-line react-hooks/exhaustive-deps

    // items 变动 检测是否超出
    if (hasVerticalScroll(tabListRef.current as Element)) {
      // 再执行下item 可见数，如果没有，还是不显示
      const tabList = getNotVisibleItem(tabListRef.current as Element, items);
      setShowMore(!!tabList.length);
    } else {
      setShowMore(false);
    }
  }, [items]);

  // 组件项列表， 筛选出当前激活或渲染过的
  const ItemComp = useMemo(() => {
    return children?.filter(
      (item: XTabItemProps) =>
        tabItemMap.current[item.key]?.rendering || item.key === curKey,
    );
  }, [children, curKey, tabItemMap]);
  return (
    <div className={`xtab ${isMobile() ? 'mobile' : ''}`} ref={xtabRef}>
      <div className="xtab-list">
        <div className="xtab-list-wrapper" ref={tabListRef}>
          {items?.map((item) => {
            // const isNeedToolTip =
            //   getTextyWidthAndHeight(item.label, {
            //     'font-size': '16px',
            //     width: isMobile() ? '130px' : '275px',
            //   })?.height > 20;
            return (
              <div
                key={item.key}
                className={
                  curKey === item.key ? `selected ${item.key}` : `${item.key}`
                }
                onClick={() => switchTabItem(item.key)}
              >
                <span>
                  {item.label}
                  {item.richTips && (
                    <Popover
                      placement="right"
                      content={
                        <div
                          className="tab-rich-tips"
                          dangerouslySetInnerHTML={{ __html: item.richTips }}
                        ></div>
                      }
                    >
                      <ExclamationCircleOutlined
                        style={{
                          width: '12px',
                          height: '12px',
                          marginLeft: '8px',
                        }}
                      />
                    </Popover>
                  )}
                </span>
              </div>
            );
          })}
        </div>
        {/* 移动端不展示点点点 */}
        {showMore && !isMobile() && (
          <Dropdown
            overlayClassName="lego-xtab-item-list-dropdown"
            getPopupContainer={(triggerNode) =>
              triggerNode.parentNode.parentNode
            }
            menu={{
              items: dropdownMenu,
              onClick: ({ key }) => {
                const find = dropdownMenu.find((item) => item.key === key);
                if (find) {
                  tabListRef.current?.scrollTo({
                    left: find.width,
                    behavior: 'smooth',
                  });
                }
                switchTabItem(key);
              },
            }}
            onOpenChange={(oepn) => {
              if (oepn) {
                setDropdownMenu(
                  getNotVisibleItem(tabListRef.current as Element, items),
                );
              }
            }}
          >
            <div className="xtab-list-operations">
              <DashOutlined />
            </div>
          </Dropdown>
        )}
      </div>
      {ItemComp.map((node) => {
        return (
          <div
            key={node.key}
            style={{ display: curKey === node.key ? 'block' : 'none' }}
            className={`xtab-item-wrapper xtab-item-wrapper-active`}
          >
            {node}
          </div>
        );
      })}
    </div>
  );
};

XTab.displayName = '选项卡';
