import { Coordinate } from '@blmcp/charts';
import { ComponentProps } from '@/pages/lego/type';
import {
  formatValue,
  transformDataSet,
} from '@/pages/lego/libraryMaterials/module/utils';
import ComponentWrapper from '../../components/module/ComponentWrapper';

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: 'X轴',
          key: 'dimensionInfo',
          // 维度聚合方式
          combineModeDim: {},
          // 指标聚合方式
          combineModeIndex: {},
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
          limit: [1, 1],
          ignoreSetDefaultComputeType: true,
        },
        {
          label: 'Y轴',
          key: 'measureInfo',
          // 同环比依赖检测的key
          rateDependKey: 'dimensionInfo',
          dateFormatDisabled: true,
          numberFormatConfig: true,
          // 指标聚合方式
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
            quick: [
              {
                key: 'rate',
                label: '同环比',
              },
              {
                key: 'percent',
                label: '百分比',
                column: true,
              },
            ],
          },
          // 维度聚合方式
          combineModeDim: {
            aggregate: [
              {
                key: 1,
                label: '计数',
              },
              {
                key: 2,
                label: '去重计数',
              },
            ],
          },
          limit: [1, Infinity],
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
      ],
    },
  },
  // 是否隐藏标题
  titleHiddenType: {
    componentName: 'TitleHiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 3,
  // 该组件用到的数据类型
  dataType: 8,
  // 组件最多一行几个
  maxRows: 3,
  // 是否可以关联等等
};

export const LineChart = function (props: ComponentProps<any>) {
  return ComponentWrapper(Coordinate, {
    handleData(dataSource) {
      return transformDataSet(dataSource, (type) => {
        if (type === 'measureInfo') {
          return {
            seriesType: 'line',
          };
        }
      });
    },
    handleProps(props, dataSource) {
      return {
        ...props,
        tooltipFormatter: formatValue,
        yAxis0Formatter: (value: number, dimensions: any[]) => {
          if (
            dimensions.every(
              (e) =>
                e.advanceComputeModeId === 30 ||
                e.valueFormat === 2 ||
                e.numberFormat === 2,
            )
          ) {
            return Number((value * 100).toFixed(2)) + '%';
          }
          return Number(value.toFixed(2));
        },
        yAxis1Formatter: () => {},
        label: dataSource?.values?.length === 1,
      };
    },
  })(props);
};

LineChart.displayName = '折线图';
