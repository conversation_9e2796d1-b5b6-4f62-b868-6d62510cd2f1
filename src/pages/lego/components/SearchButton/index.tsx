import { useRef, useState } from 'react';
import { Button } from '@blmcp/ui';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import relationCenterExp from '../../libraryMaterials/module/RelationCenter';
import './index.less';
import linkageCenterExp from '../../libraryMaterials/module/Linkage';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import { globalCache } from '@/pages/lego/utils/cache';
import isMobile from '../../utils/isMobile';
import Icon from './Icon.jsx';

const waitTime = 1000;

export const SearchButton = (props) => {
  const linkageCenter = linkageCenterExp(props.uuid);
  const queryCenter = queryCenterExp(props.uuid);
  const relationCenter = relationCenterExp(props.uuid);
  const [loading, setLoading] = useState(false);
  const [expandText, setExpandText] = useState('展开筛选项');
  const filterLinkComponents = props.filterLinkComponents;
  const search = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, waitTime);
    queryCenter.resetTableQuery();

    // TODO 后续新增图表需要配置类型 筛选器的数据不清空
    globalCache.clear([1, 2, 3, 5]);

    // 触发筛选器查询
    if (filterLinkComponents && filterLinkComponents.length > 0) {
      filterLinkComponents.forEach((item) => {
        relationCenter.notify('all', item);
      });
    } else {
      relationCenter.notify('all');
    }

    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00003812',
      pageId: 'p_leopard_cp_00000884',
      ext: {
        str0_e: props.uuid,
      },
    });
  };
  const reset = () => {
    // 重置，走联动规则
    const resetKey = `reset${props.partialContainerFilterId || ''}`;
    linkageCenter.notify(resetKey);
    // TODO 后续新增图表需要配置类型 筛选器的数据不清空
    globalCache.clear([1, 2, 3, 5]);
    queryCenter.resetTableQuery();
    if (filterLinkComponents && filterLinkComponents.length > 0) {
      filterLinkComponents.forEach((item) => {
        relationCenter.notify('all', item);
      });
    } else {
      relationCenter.notify('all');
    }
  };
  const wrapRef = useRef();
  const onExpand = () => {
    wrapRef.current?.parentNode?.parentNode?.classList?.toggle('expand');
    setExpandText(expandText === '展开筛选项' ? '收起筛选项' : '展开筛选项');
  };
  return (
    <div
      className={`lego-search-wrap ${isMobile() ? 'isMobile' : ''}`}
      ref={wrapRef}
    >
      <Button className="lego-search-btn" onClick={reset}>
        重置
      </Button>
      <Button type="primary" onClick={search} loading={loading}>
        查询
      </Button>
      {isMobile() ? (
        <div className="condition-expand-icon" onClick={onExpand}>
          <span style={{ marginRight: '5px' }}>{expandText}</span>
          <Icon></Icon>
        </div>
      ) : null}
    </div>
  );
};
