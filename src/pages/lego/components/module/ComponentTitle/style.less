.lego-bi-compontent-title-box {
  margin: 0 -12px;
  padding: 0 12px;
  border-bottom: 1px solid #000;
  border-color: rgba(37, 52, 79, 0.0784);
  padding-bottom: 12px;
  margin-bottom: 8px;
  height: 28px;
  display: flex;
  align-items: center;
  &.mobile {
    // 处理移动端兼容问题
    position: relative;
    border-bottom: 0;
    margin-bottom: 0;
    .arrow-svg {
      margin-top: 6px;
      margin-left: 5px;
    }
    h1 {
      font-size: 16px;
      height: 18px;
      line-height: 18px;
    }
  }

  .lego-bi-compontent-title {
    width: 0;
    flex: 1;
    display: flex;
    align-items: center;
    input {
      font-size: 13px;
      line-height: 1;
      color: #000;
      font-weight: 500;
      padding: 0;
    }
    .data-time-tag {
      .ant-tag {
        border: none;
      }
    }
  }

  h1 {
    max-width: 100%;
    color: #000;
    font-size: 13px;
    font-weight: 500;
    overflow: hidden;
    // height: 15px; // 解决文字底部截取问题
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 3px;
    outline: none;
    padding: 0;
    margin: 0 5px 0 0;
  }

  svg {
    cursor: pointer;
    width: 14px;
    height: 14px;
  }

  .lego-bi-compontent-title-fun {
    display: none;

    > svg {
      margin-right: 10px;
    }

    > svg:last-child {
      margin: 0;
    }
  }
}

.lego-bi-compontent-active,
.lego-card-wrap:hover .lego-bi-compontent-title-fun {
  display: block;
}

.lego-export {
  // position: absolute;
  // right: 0px;
  // top: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
